
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/auth/AuthService.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">core/auth</a> AuthService.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.41% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>20/103</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/11</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.41% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>20/103</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { openDb } from '../../db/openDb'
import { migrate } from '../../db/migrations'
import { ulid } from '../../lib/ids'
&nbsp;
export type Session = { id: string; accountId: string; createdAt: number; expiresAt?: number }
&nbsp;
// In-memory session and vault key gate
let currentSession: Session | null = null
&nbsp;
// PBKDF2 params (can be migrated later to Argon2id behind flag)
const PBKDF2_PARAMS = {
  iterations: 200_000,
  hash: 'SHA-256' as const,
  saltBytes: 16,
  keyLen: 32
}
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function hkdf(bytes: ArrayBuffer): Promise&lt;string&gt; {</span></span>
  // Return hex string for constant-time compare use
<span class="cstat-no" title="statement not covered" >  const arr = new Uint8Array(bytes)</span>
<span class="cstat-no" title="statement not covered" >  return Array.from(arr).map(b =&gt; b.toString(16).padStart(2, '0')).join('')</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function derivePinHash(pin: string, salt: Uint8Array): Promise&lt;Uint8Array&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >  const enc = new TextEncoder()</span>
<span class="cstat-no" title="statement not covered" >  const keyMaterial = await crypto.subtle.importKey(</span>
<span class="cstat-no" title="statement not covered" >    'raw',</span>
<span class="cstat-no" title="statement not covered" >    enc.encode(pin),</span>
<span class="cstat-no" title="statement not covered" >    { name: 'PBKDF2' },</span>
<span class="cstat-no" title="statement not covered" >    false,</span>
<span class="cstat-no" title="statement not covered" >    ['deriveBits']</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >  const bits = await crypto.subtle.deriveBits(</span>
<span class="cstat-no" title="statement not covered" >    { name: 'PBKDF2', salt, iterations: PBKDF2_PARAMS.iterations, hash: PBKDF2_PARAMS.hash },</span>
<span class="cstat-no" title="statement not covered" >    keyMaterial,</span>
<span class="cstat-no" title="statement not covered" >    PBKDF2_PARAMS.keyLen * 8</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >  return new Uint8Array(bits)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function timingSafeEqual(a: Uint8Array, b: Uint8Array): boolean {</span></span>
<span class="cstat-no" title="statement not covered" >  if (a.length !== b.length) return false</span>
<span class="cstat-no" title="statement not covered" >  let diff = 0</span>
<span class="cstat-no" title="statement not covered" >  for (let i = 0; i &lt; a.length; i++) diff |= a[i] ^ b[i]</span>
<span class="cstat-no" title="statement not covered" >  return diff === 0</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
export const Auth = {
<span class="fstat-no" title="function not covered" >  async ensureLocalAccount(): Promise&lt;string&gt; {</span>
<span class="cstat-no" title="statement not covered" >    await migrate()</span>
<span class="cstat-no" title="statement not covered" >    const db = await openDb() as any</span>
<span class="cstat-no" title="statement not covered" >    const existing = db.selectValue(`SELECT id FROM accounts LIMIT 1`)</span>
<span class="cstat-no" title="statement not covered" >    if (existing) return existing</span>
<span class="cstat-no" title="statement not covered" >    const id = ulid()</span>
<span class="cstat-no" title="statement not covered" >    const now = Date.now()</span>
<span class="cstat-no" title="statement not covered" >    db.exec(`INSERT INTO accounts(id,display_name,created_at) VALUES(?,?,?)`, [id, 'Local User', now])</span>
<span class="cstat-no" title="statement not covered" >    return id</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  async setPin(accountId: string, pin: string): Promise&lt;void&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const db = await openDb() as any</span>
<span class="cstat-no" title="statement not covered" >    const salt = crypto.getRandomValues(new Uint8Array(PBKDF2_PARAMS.saltBytes))</span>
<span class="cstat-no" title="statement not covered" >    const hash = await derivePinHash(pin, salt)</span>
<span class="cstat-no" title="statement not covered" >    const now = Date.now()</span>
<span class="cstat-no" title="statement not covered" >    const params = JSON.stringify({ kdf: 'pbkdf2', iters: PBKDF2_PARAMS.iterations, hash: PBKDF2_PARAMS.hash, len: PBKDF2_PARAMS.keyLen })</span>
<span class="cstat-no" title="statement not covered" >    db.exec(`INSERT OR REPLACE INTO pins(account_id,salt,hash,params,updated_at) VALUES(?,?,?,?,?)`, [accountId, salt, hash, params, now])</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  async verifyPin(accountId: string, pin: string): Promise&lt;Session&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const db = await openDb() as any</span>
<span class="cstat-no" title="statement not covered" >    const rows:any[] = []</span>
<span class="cstat-no" title="statement not covered" >    db.exec({</span>
<span class="cstat-no" title="statement not covered" >      sql: `SELECT salt, hash, params, failed_attempts, next_allowed_at FROM pins WHERE account_id = ?`,</span>
<span class="cstat-no" title="statement not covered" >      bind: [accountId],</span>
<span class="cstat-no" title="statement not covered" >      rowMode: 'object',</span>
<span class="cstat-no" title="statement not covered" >      callback: (r:any) =&gt; rows.push(r)</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >    if (!rows.length) throw new Error('PIN not set')</span>
<span class="cstat-no" title="statement not covered" >    const row = rows[0]</span>
<span class="cstat-no" title="statement not covered" >    const saltBuf: Uint8Array = row.salt instanceof Uint8Array ? row.salt : new Uint8Array(row.salt)</span>
<span class="cstat-no" title="statement not covered" >    const hashStored: Uint8Array = row.hash instanceof Uint8Array ? row.hash : new Uint8Array(row.hash)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const now = Date.now()</span>
<span class="cstat-no" title="statement not covered" >    if (typeof row.next_allowed_at === 'number' &amp;&amp; now &lt; row.next_allowed_at) {</span>
<span class="cstat-no" title="statement not covered" >      const secs = Math.ceil((row.next_allowed_at - now) / 1000)</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`Locked. Try again in ${secs}s`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const iterations = (() =&gt; { try { return JSON.parse(row.params).iterations ?? PBKDF2_PARAMS.iterations } catch { return PBKDF2_PARAMS.iterations } })()</span>
<span class="cstat-no" title="statement not covered" >    const hashCandidate = await derivePinHash(pin, saltBuf)</span>
<span class="cstat-no" title="statement not covered" >    if (!timingSafeEqual(hashStored, hashCandidate)) {</span>
<span class="cstat-no" title="statement not covered" >      const newFails = (row.failed_attempts ?? 0) + 1</span>
<span class="cstat-no" title="statement not covered" >      const until = newFails &gt;= 5 ? now + 5 * 60_000 : null</span>
<span class="cstat-no" title="statement not covered" >      db.exec(`UPDATE pins SET failed_attempts=?, next_allowed_at=? WHERE account_id=?`, [newFails, until, accountId])</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(newFails &gt;= 5 ? 'Too many attempts. Try later.' : 'Incorrect PIN')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // success: reset counters
<span class="cstat-no" title="statement not covered" >    db.exec(`UPDATE pins SET failed_attempts=0, next_allowed_at=NULL WHERE account_id=?`, [accountId])</span>
&nbsp;
    // Create session
<span class="cstat-no" title="statement not covered" >    const session: Session = { id: ulid(), accountId, createdAt: Date.now() }</span>
<span class="cstat-no" title="statement not covered" >    db.exec(`INSERT INTO sessions(id,account_id,created_at) VALUES(?,?,?)`, [session.id, accountId, session.createdAt])</span>
<span class="cstat-no" title="statement not covered" >    currentSession = session</span>
<span class="cstat-no" title="statement not covered" >    return session</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  async registerWebAuthn(accountId: string): Promise&lt;void&gt; {</span>
    // TODO: Implement WebAuthn registration with local ceremony
    // - Create options (publicKey)
    // - navigator.credentials.create
    // - Store publicKey JWK + credId in credentials
<span class="cstat-no" title="statement not covered" >    throw new Error('WebAuthn registration not implemented yet')</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  async authenticateWebAuthn(accountId: string): Promise&lt;Session&gt; {</span>
    // TODO: Implement WebAuthn authentication with local verification
    // - navigator.credentials.get
    // - Verify signature locally
    // - Create session
<span class="cstat-no" title="statement not covered" >    throw new Error('WebAuthn authentication not implemented yet')</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  currentSession(): Session | null {</span>
<span class="cstat-no" title="statement not covered" >    return currentSession</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  lock(): void {</span>
    // Wipe in-memory session (and later, in-memory vault key)
<span class="cstat-no" title="statement not covered" >    currentSession = null</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
<span class="fstat-no" title="function not covered" >  async unlockWithSession(session: Session): Promise&lt;void&gt; {</span>
    // Later: hydrate the in-memory vault key here based on session
    // For now, just set the session reference
<span class="cstat-no" title="statement not covered" >    currentSession = session</span>
<span class="cstat-no" title="statement not covered" >  }</span>
}
&nbsp;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-11T15:47:26.586Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    