{"C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\core\\auth\\AuthService.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\core\\auth\\AuthService.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 45}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 41}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 23}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 22}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 27}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 16}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 12}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 1}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 58}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 35}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 75}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 1}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 82}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 31}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 52}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 10}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 20}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 23}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 10}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 18}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 3}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 46}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 93}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 16}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 28}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 3}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 29}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 1}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 65}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 41}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 14}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 56}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 19}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 1}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 21}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 47}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 19}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 36}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 70}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 33}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 21}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 26}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 102}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 13}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 4}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 63}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 36}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 80}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 47}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 26}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 138}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 138}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 4}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 69}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 36}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 25}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 13}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 104}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 24}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 24}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 39}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 6}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 52}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 23}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 100}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 103}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 26}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 79}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 64}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 54}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 5}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 155}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 59}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 54}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 53}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 59}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 118}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 88}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 5}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 102}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 77}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 119}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 28}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 18}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 4}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 60}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 64}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 4}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 67}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 66}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 4}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 36}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 25}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 4}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 16}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 25}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 4}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 60}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 28}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 3}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "7": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "17": 0, "19": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "47": 1, "48": 1, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "59": 1, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "68": 1, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "98": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "107": 1, "112": 0, "113": 0, "115": 1, "120": 0, "121": 0, "123": 1, "124": 0, "125": 0, "127": 1, "129": 0, "130": 0, "132": 1, "135": 0, "136": 0, "137": 1}, "branchMap": {}, "b": {}, "fnMap": {"0": {"name": "hkdf", "decl": {"start": {"line": 18, "column": 0}, "end": {"line": 22, "column": 1}}, "loc": {"start": {"line": 18, "column": 0}, "end": {"line": 22, "column": 1}}, "line": 18}, "1": {"name": "derivePinHash", "decl": {"start": {"line": 24, "column": 0}, "end": {"line": 39, "column": 1}}, "loc": {"start": {"line": 24, "column": 0}, "end": {"line": 39, "column": 1}}, "line": 24}, "2": {"name": "timingSafeEqual", "decl": {"start": {"line": 41, "column": 0}, "end": {"line": 46, "column": 1}}, "loc": {"start": {"line": 41, "column": 0}, "end": {"line": 46, "column": 1}}, "line": 41}, "3": {"name": "ensureLocalAccount", "decl": {"start": {"line": 49, "column": 2}, "end": {"line": 58, "column": 4}}, "loc": {"start": {"line": 49, "column": 2}, "end": {"line": 58, "column": 4}}, "line": 49}, "4": {"name": "setPin", "decl": {"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 4}}, "loc": {"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 4}}, "line": 60}, "5": {"name": "verifyPin", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 106, "column": 4}}, "loc": {"start": {"line": 69, "column": 2}, "end": {"line": 106, "column": 4}}, "line": 69}, "6": {"name": "registerWebAuthn", "decl": {"start": {"line": 108, "column": 2}, "end": {"line": 114, "column": 4}}, "loc": {"start": {"line": 108, "column": 2}, "end": {"line": 114, "column": 4}}, "line": 108}, "7": {"name": "authenticateWebAuthn", "decl": {"start": {"line": 116, "column": 2}, "end": {"line": 122, "column": 4}}, "loc": {"start": {"line": 116, "column": 2}, "end": {"line": 122, "column": 4}}, "line": 116}, "8": {"name": "currentSession", "decl": {"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 4}}, "loc": {"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 4}}, "line": 124}, "9": {"name": "lock", "decl": {"start": {"line": 128, "column": 2}, "end": {"line": 131, "column": 4}}, "loc": {"start": {"line": 128, "column": 2}, "end": {"line": 131, "column": 4}}, "line": 128}, "10": {"name": "unlockWithSession", "decl": {"start": {"line": 133, "column": 2}, "end": {"line": 137, "column": 3}}, "loc": {"start": {"line": 133, "column": 2}, "end": {"line": 137, "column": 3}}, "line": 133}}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\core\\auth\\idleLock.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\core\\auth\\idleLock.ts", "all": true, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 15}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 45}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 18}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 22}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 28}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 16}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 65}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 17}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 1}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 31}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 96}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 58}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 5}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 10}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 1}}}, "s": {"1": 0, "3": 0, "4": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}, "locations": [{"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}, "loc": {"start": {"line": 1, "column": 497}, "end": {"line": 20, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\db\\dao.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\db\\dao.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 39}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 34}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 47}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 43}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 47}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 99}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 19}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 37}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 3}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 1}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 50}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 94}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 28}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 25}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 150}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 21}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 1}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 133}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 52}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 55}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 33}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 30}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 113}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 30}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 25}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 6}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 3}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 28}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 25}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 20}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 167}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 228}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 25}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 9}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 11}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 15}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 18}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 24}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 39}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 22}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 20}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 18}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 6}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 64}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 19}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 62}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 3}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 12}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 1}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 123}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 28}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 25}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 164}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 21}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 1}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 123}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 28}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 20}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 156}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 12}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 1}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 165}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 28}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 25}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 192}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 21}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 1}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 212}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 61}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 62}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 33}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 30}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 140}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 37}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 25}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 6}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 3}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 28}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 20}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 261}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 12}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 1}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 73}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 28}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 105}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 1}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 56}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 28}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 71}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 1}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 120}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 40}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 52}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 10}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 12}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 101}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 12}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 34}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 4}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 1}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 137}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 49}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 59}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 10}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 12}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 120}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 12}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 41}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 4}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 1}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 71}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 28}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 103}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 116}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 107}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 105}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 10}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 19}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 19}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 14}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 13}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 35}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 4}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 1}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 127}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 7}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 56}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 86}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 53}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 22}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 19}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 62}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 14}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 3}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "7": 1, "8": 1, "9": 5, "10": 5, "11": 5, "12": 1, "14": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "23": 5, "25": 5, "26": 5, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 2, "33": 2, "35": 3, "36": 3, "37": 3, "40": 3, "41": 3, "44": 3, "45": 3, "46": 3, "47": 3, "48": 3, "49": 3, "50": 3, "51": 3, "52": 3, "53": 3, "54": 3, "55": 3, "56": 3, "57": 5, "58": 0, "60": 0, "62": 2, "63": 2, "66": 1, "67": 1, "68": 1, "69": 1, "70": 1, "71": 1, "73": 1, "74": 1, "75": 1, "76": 1, "77": 1, "78": 1, "81": 1, "82": 1, "83": 1, "84": 1, "85": 1, "86": 1, "88": 4, "90": 4, "91": 4, "92": 2, "93": 2, "94": 2, "95": 2, "96": 2, "97": 2, "98": 2, "100": 2, "101": 2, "102": 2, "103": 2, "104": 2, "106": 7, "107": 7, "108": 7, "109": 7, "111": 9, "112": 9, "113": 9, "114": 9, "117": 2, "118": 2, "119": 2, "120": 2, "121": 2, "122": 2, "123": 2, "124": 2, "125": 2, "126": 2, "128": 2, "129": 2, "130": 2, "131": 2, "132": 2, "133": 2, "134": 2, "135": 2, "136": 2, "137": 2, "140": 0, "141": 0, "144": 0, "145": 0, "148": 0, "151": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "163": 0, "164": 0, "165": 0, "166": 0, "169": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0}, "branchMap": {"0": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 2}, "end": {"line": 12, "column": 3}}, "locations": [{"start": {"line": 9, "column": 2}, "end": {"line": 12, "column": 3}}]}, "1": {"type": "branch", "line": 15, "loc": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 50}}, "locations": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 50}}]}, "2": {"type": "branch", "line": 17, "loc": {"start": {"line": 17, "column": 0}, "end": {"line": 22, "column": 1}}, "locations": [{"start": {"line": 17, "column": 0}, "end": {"line": 22, "column": 1}}]}, "3": {"type": "branch", "line": 20, "loc": {"start": {"line": 20, "column": 125}, "end": {"line": 20, "column": 147}}, "locations": [{"start": {"line": 20, "column": 125}, "end": {"line": 20, "column": 147}}]}, "4": {"type": "branch", "line": 24, "loc": {"start": {"line": 24, "column": 0}, "end": {"line": 64, "column": 1}}, "locations": [{"start": {"line": 24, "column": 0}, "end": {"line": 64, "column": 1}}]}, "5": {"type": "branch", "line": 27, "loc": {"start": {"line": 27, "column": 54}, "end": {"line": 34, "column": 3}}, "locations": [{"start": {"line": 27, "column": 54}, "end": {"line": 34, "column": 3}}]}, "6": {"type": "branch", "line": 34, "loc": {"start": {"line": 34, "column": 2}, "end": {"line": 57, "column": 64}}, "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 57, "column": 64}}]}, "7": {"type": "branch", "line": 57, "loc": {"start": {"line": 57, "column": 62}, "end": {"line": 58, "column": 11}}, "locations": [{"start": {"line": 57, "column": 62}, "end": {"line": 58, "column": 11}}]}, "8": {"type": "branch", "line": 58, "loc": {"start": {"line": 58, "column": 2}, "end": {"line": 61, "column": 3}}, "locations": [{"start": {"line": 58, "column": 2}, "end": {"line": 61, "column": 3}}]}, "9": {"type": "branch", "line": 61, "loc": {"start": {"line": 61, "column": 2}, "end": {"line": 64, "column": 1}}, "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 64, "column": 1}}]}, "10": {"type": "branch", "line": 67, "loc": {"start": {"line": 67, "column": 0}, "end": {"line": 72, "column": 1}}, "locations": [{"start": {"line": 67, "column": 0}, "end": {"line": 72, "column": 1}}]}, "11": {"type": "branch", "line": 70, "loc": {"start": {"line": 70, "column": 139}, "end": {"line": 70, "column": 161}}, "locations": [{"start": {"line": 70, "column": 139}, "end": {"line": 70, "column": 161}}]}, "12": {"type": "branch", "line": 74, "loc": {"start": {"line": 74, "column": 0}, "end": {"line": 79, "column": 1}}, "locations": [{"start": {"line": 74, "column": 0}, "end": {"line": 79, "column": 1}}]}, "13": {"type": "branch", "line": 82, "loc": {"start": {"line": 82, "column": 0}, "end": {"line": 87, "column": 1}}, "locations": [{"start": {"line": 82, "column": 0}, "end": {"line": 87, "column": 1}}]}, "14": {"type": "branch", "line": 85, "loc": {"start": {"line": 85, "column": 167}, "end": {"line": 85, "column": 189}}, "locations": [{"start": {"line": 85, "column": 167}, "end": {"line": 85, "column": 189}}]}, "15": {"type": "branch", "line": 89, "loc": {"start": {"line": 89, "column": 0}, "end": {"line": 105, "column": 1}}, "locations": [{"start": {"line": 89, "column": 0}, "end": {"line": 105, "column": 1}}]}, "16": {"type": "branch", "line": 92, "loc": {"start": {"line": 92, "column": 61}, "end": {"line": 105, "column": 1}}, "locations": [{"start": {"line": 92, "column": 61}, "end": {"line": 105, "column": 1}}]}, "17": {"type": "branch", "line": 107, "loc": {"start": {"line": 107, "column": 0}, "end": {"line": 110, "column": 1}}, "locations": [{"start": {"line": 107, "column": 0}, "end": {"line": 110, "column": 1}}]}, "18": {"type": "branch", "line": 109, "loc": {"start": {"line": 109, "column": 98}, "end": {"line": 109, "column": 105}}, "locations": [{"start": {"line": 109, "column": 98}, "end": {"line": 109, "column": 105}}]}, "19": {"type": "branch", "line": 112, "loc": {"start": {"line": 112, "column": 0}, "end": {"line": 115, "column": 1}}, "locations": [{"start": {"line": 112, "column": 0}, "end": {"line": 115, "column": 1}}]}, "20": {"type": "branch", "line": 114, "loc": {"start": {"line": 114, "column": 64}, "end": {"line": 114, "column": 71}}, "locations": [{"start": {"line": 114, "column": 64}, "end": {"line": 114, "column": 71}}]}, "21": {"type": "branch", "line": 118, "loc": {"start": {"line": 118, "column": 0}, "end": {"line": 127, "column": 1}}, "locations": [{"start": {"line": 118, "column": 0}, "end": {"line": 127, "column": 1}}]}, "22": {"type": "branch", "line": 123, "loc": {"start": {"line": 123, "column": 12}, "end": {"line": 123, "column": 34}}, "locations": [{"start": {"line": 123, "column": 12}, "end": {"line": 123, "column": 34}}]}, "23": {"type": "branch", "line": 123, "loc": {"start": {"line": 123, "column": 22}, "end": {"line": 123, "column": 101}}, "locations": [{"start": {"line": 123, "column": 22}, "end": {"line": 123, "column": 101}}]}, "24": {"type": "branch", "line": 129, "loc": {"start": {"line": 129, "column": 0}, "end": {"line": 138, "column": 1}}, "locations": [{"start": {"line": 129, "column": 0}, "end": {"line": 138, "column": 1}}]}, "25": {"type": "branch", "line": 134, "loc": {"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": 34}}, "locations": [{"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": 34}}]}, "26": {"type": "branch", "line": 134, "loc": {"start": {"line": 134, "column": 22}, "end": {"line": 134, "column": 120}}, "locations": [{"start": {"line": 134, "column": 22}, "end": {"line": 134, "column": 120}}]}}, "b": {"0": [5], "1": [1], "2": [1], "3": [2], "4": [5], "5": [2], "6": [3], "7": [2], "8": [0], "9": [2], "10": [1], "11": [2], "12": [1], "13": [1], "14": [2], "15": [4], "16": [2], "17": [7], "18": [0], "19": [9], "20": [3], "21": [2], "22": [1], "23": [1], "24": [2], "25": [1], "26": [1]}, "fnMap": {"0": {"name": "FreeTierLimitError", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 12, "column": 3}}, "loc": {"start": {"line": 9, "column": 2}, "end": {"line": 12, "column": 3}}, "line": 9}, "1": {"name": "initDb", "decl": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 50}}, "loc": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 50}}, "line": 15}, "2": {"name": "listProjects", "decl": {"start": {"line": 17, "column": 0}, "end": {"line": 22, "column": 1}}, "loc": {"start": {"line": 17, "column": 0}, "end": {"line": 22, "column": 1}}, "line": 17}, "3": {"name": "callback", "decl": {"start": {"line": 20, "column": 125}, "end": {"line": 20, "column": 147}}, "loc": {"start": {"line": 20, "column": 125}, "end": {"line": 20, "column": 147}}, "line": 20}, "4": {"name": "createProject", "decl": {"start": {"line": 24, "column": 0}, "end": {"line": 64, "column": 1}}, "loc": {"start": {"line": 24, "column": 0}, "end": {"line": 64, "column": 1}}, "line": 24}, "5": {"name": "listJunctions", "decl": {"start": {"line": 67, "column": 0}, "end": {"line": 72, "column": 1}}, "loc": {"start": {"line": 67, "column": 0}, "end": {"line": 72, "column": 1}}, "line": 67}, "6": {"name": "callback", "decl": {"start": {"line": 70, "column": 139}, "end": {"line": 70, "column": 161}}, "loc": {"start": {"line": 70, "column": 139}, "end": {"line": 70, "column": 161}}, "line": 70}, "7": {"name": "createJunction", "decl": {"start": {"line": 74, "column": 0}, "end": {"line": 79, "column": 1}}, "loc": {"start": {"line": 74, "column": 0}, "end": {"line": 79, "column": 1}}, "line": 74}, "8": {"name": "listSegments", "decl": {"start": {"line": 82, "column": 0}, "end": {"line": 87, "column": 1}}, "loc": {"start": {"line": 82, "column": 0}, "end": {"line": 87, "column": 1}}, "line": 82}, "9": {"name": "callback", "decl": {"start": {"line": 85, "column": 167}, "end": {"line": 85, "column": 189}}, "loc": {"start": {"line": 85, "column": 167}, "end": {"line": 85, "column": 189}}, "line": 85}, "10": {"name": "createSegment", "decl": {"start": {"line": 89, "column": 0}, "end": {"line": 105, "column": 1}}, "loc": {"start": {"line": 89, "column": 0}, "end": {"line": 105, "column": 1}}, "line": 89}, "11": {"name": "countSegments", "decl": {"start": {"line": 107, "column": 0}, "end": {"line": 110, "column": 1}}, "loc": {"start": {"line": 107, "column": 0}, "end": {"line": 110, "column": 1}}, "line": 107}, "12": {"name": "countProjects", "decl": {"start": {"line": 112, "column": 0}, "end": {"line": 115, "column": 1}}, "loc": {"start": {"line": 112, "column": 0}, "end": {"line": 115, "column": 1}}, "line": 112}, "13": {"name": "canCreateProject", "decl": {"start": {"line": 118, "column": 0}, "end": {"line": 127, "column": 1}}, "loc": {"start": {"line": 118, "column": 0}, "end": {"line": 127, "column": 1}}, "line": 118}, "14": {"name": "canCreateSegment", "decl": {"start": {"line": 129, "column": 0}, "end": {"line": 138, "column": 1}}, "loc": {"start": {"line": 129, "column": 0}, "end": {"line": 138, "column": 1}}, "line": 129}, "15": {"name": "getProjectData", "decl": {"start": {"line": 141, "column": 0}, "end": {"line": 161, "column": 1}}, "loc": {"start": {"line": 141, "column": 0}, "end": {"line": 161, "column": 1}}, "line": 141}, "16": {"name": "createProjectSnapshot", "decl": {"start": {"line": 164, "column": 0}, "end": {"line": 177, "column": 1}}, "loc": {"start": {"line": 164, "column": 0}, "end": {"line": 177, "column": 1}}, "line": 164}}, "f": {"0": 5, "1": 1, "2": 1, "3": 2, "4": 5, "5": 1, "6": 2, "7": 1, "8": 1, "9": 2, "10": 4, "11": 7, "12": 9, "13": 2, "14": 2, "15": 0, "16": 0}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\db\\migrations.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\db\\migrations.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 34}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 32}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 28}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 102}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 71}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 90}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 20}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 37}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 22}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 12}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 17}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 5}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 86}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 4}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 21}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 5}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 37}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 5}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 34}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 5}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 21}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 5}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 42}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 85}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 53}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 31}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 9}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 123}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 104}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 24}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 17}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 26}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 14}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 5}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 5}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 1}}}, "s": {"0": 1, "2": 15, "3": 15, "4": 14, "5": 14, "6": 61, "7": 61, "8": 61, "9": 45, "10": 61, "11": 11, "12": 11, "13": 56, "14": 61, "16": 14, "72": 14, "74": 14, "78": 14, "80": 14, "111": 14, "114": 14, "151": 14, "154": 14, "155": 11, "156": 11, "157": 11, "158": 11, "159": 11, "160": 11, "161": 11, "162": 11, "163": 0, "164": 0, "165": 0, "166": 14, "167": 14}, "branchMap": {"0": {"type": "branch", "line": 3, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}, "locations": [{"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}]}, "1": {"type": "branch", "line": 4, "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 168, "column": 1}}, "locations": [{"start": {"line": 4, "column": 26}, "end": {"line": 168, "column": 1}}]}, "2": {"type": "branch", "line": 6, "loc": {"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}, "locations": [{"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}]}, "3": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 20}}, "locations": [{"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 20}}]}, "4": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 13}, "end": {"line": 9, "column": 36}}, "locations": [{"start": {"line": 8, "column": 13}, "end": {"line": 9, "column": 36}}]}, "5": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 36}, "end": {"line": 11, "column": 11}}, "locations": [{"start": {"line": 9, "column": 36}, "end": {"line": 11, "column": 11}}]}, "6": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 4}, "end": {"line": 13, "column": 5}}, "locations": [{"start": {"line": 11, "column": 4}, "end": {"line": 13, "column": 5}}]}, "7": {"type": "branch", "line": 13, "loc": {"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 86}}, "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 86}}]}, "8": {"type": "branch", "line": 155, "loc": {"start": {"line": 155, "column": 28}, "end": {"line": 167, "column": 3}}, "locations": [{"start": {"line": 155, "column": 28}, "end": {"line": 167, "column": 3}}]}, "9": {"type": "branch", "line": 156, "loc": {"start": {"line": 156, "column": 77}, "end": {"line": 156, "column": 85}}, "locations": [{"start": {"line": 156, "column": 77}, "end": {"line": 156, "column": 85}}]}, "10": {"type": "branch", "line": 163, "loc": {"start": {"line": 163, "column": 4}, "end": {"line": 166, "column": 5}}, "locations": [{"start": {"line": 163, "column": 4}, "end": {"line": 166, "column": 5}}]}}, "b": {"0": [15], "1": [14], "2": [61], "3": [5], "4": [56], "5": [45], "6": [11], "7": [56], "8": [11], "9": [0], "10": [0]}, "fnMap": {"0": {"name": "migrate", "decl": {"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}, "loc": {"start": {"line": 3, "column": 0}, "end": {"line": 168, "column": 1}}, "line": 3}, "1": {"name": "apply", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}, "loc": {"start": {"line": 6, "column": 16}, "end": {"line": 15, "column": 4}}, "line": 6}}, "f": {"0": 15, "1": 61}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\db\\openDb.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\db\\openDb.ts", "all": false, "statementMap": {"4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 41}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 45}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 34}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 28}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 42}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 73}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 63}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 7}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 31}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 6}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 76}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 53}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 67}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 14}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 7}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 19}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 1}}}, "s": {"4": 1, "6": 0, "7": 0, "8": 0, "11": 0, "13": 0, "14": 0, "15": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "branchMap": {}, "b": {}, "fnMap": {"0": {"name": "openDb", "decl": {"start": {"line": 7, "column": 0}, "end": {"line": 28, "column": 1}}, "loc": {"start": {"line": 7, "column": 0}, "end": {"line": 28, "column": 1}}, "line": 7}}, "f": {"0": 0}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\featureFlags.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\featureFlags.ts", "all": false, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 7}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 51}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 31}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 25}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 1}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 51}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 72}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 1}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 30}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 46}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 44}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 52}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 37}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 33}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 11}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 53}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 84}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 51}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 70}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 1}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 55}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 74}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 1}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 51}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 88}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 1}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 50}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 73}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 1}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 42}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 82}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 52}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 5}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 81}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 51}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 5}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 85}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 55}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 5}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 11}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 3}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 1}}}, "s": {"1": 1, "2": 24, "3": 24, "4": 24, "5": 24, "6": 24, "7": 1, "8": 15, "9": 15, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "21": 1, "22": 3, "23": 3, "25": 1, "26": 3, "27": 3, "29": 1, "30": 3, "31": 3, "33": 1, "34": 0, "35": 0, "37": 1, "38": 0, "39": 0, "42": 1, "43": 3, "45": 3, "46": 1, "47": 1, "48": 3, "49": 1, "50": 1, "51": 2, "52": 2, "53": 2, "54": 3, "56": 1, "57": 3}, "branchMap": {"0": {"type": "branch", "line": 2, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "locations": [{"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}]}, "1": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 11}, "end": {"line": 5, "column": 27}}, "locations": [{"start": {"line": 5, "column": 11}, "end": {"line": 5, "column": 27}}]}, "2": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 31}}, "locations": [{"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 31}}]}, "3": {"type": "branch", "line": 6, "loc": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 25}}, "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 25}}]}, "4": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "locations": [{"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}]}, "5": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 45}, "end": {"line": 9, "column": 56}}, "locations": [{"start": {"line": 9, "column": 45}, "end": {"line": 9, "column": 56}}]}, "6": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 50}, "end": {"line": 9, "column": 59}}, "locations": [{"start": {"line": 9, "column": 50}, "end": {"line": 9, "column": 59}}]}, "7": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 62}, "end": {"line": 9, "column": 72}}, "locations": [{"start": {"line": 9, "column": 62}, "end": {"line": 9, "column": 72}}]}, "8": {"type": "branch", "line": 22, "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}, "locations": [{"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}]}, "9": {"type": "branch", "line": 26, "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}, "locations": [{"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}]}, "10": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}, "locations": [{"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}]}, "11": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}, "locations": [{"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}]}, "12": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 81}, "end": {"line": 48, "column": 5}}, "locations": [{"start": {"line": 46, "column": 81}, "end": {"line": 48, "column": 5}}]}, "13": {"type": "branch", "line": 48, "loc": {"start": {"line": 48, "column": 4}, "end": {"line": 49, "column": 80}}, "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 49, "column": 80}}]}, "14": {"type": "branch", "line": 49, "loc": {"start": {"line": 49, "column": 80}, "end": {"line": 51, "column": 5}}, "locations": [{"start": {"line": 49, "column": 80}, "end": {"line": 51, "column": 5}}]}, "15": {"type": "branch", "line": 51, "loc": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}, "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}]}, "16": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}]}}, "b": {"0": [24], "1": [17], "2": [5], "3": [2], "4": [15], "5": [9], "6": [6], "7": [1], "8": [3], "9": [3], "10": [3], "11": [3], "12": [1], "13": [2], "14": [1], "15": [2], "16": [1]}, "fnMap": {"0": {"name": "flag", "decl": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "line": 2}, "1": {"name": "setFlag", "decl": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 10, "column": 1}}, "line": 8}, "2": {"name": "isVaultEncryptionEnabled", "decl": {"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}, "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 24, "column": 1}}, "line": 22}, "3": {"name": "isFreeTierGuardEnabled", "decl": {"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}, "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 28, "column": 1}}, "line": 26}, "4": {"name": "isProjectAutoBackupEnabled", "decl": {"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}, "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 32, "column": 1}}, "line": 30}, "5": {"name": "isAuthGateVaultEnabled", "decl": {"start": {"line": 34, "column": 7}, "end": {"line": 36, "column": 1}}, "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 36, "column": 1}}, "line": 34}, "6": {"name": "isAuthWebAuthnEnabled", "decl": {"start": {"line": 38, "column": 7}, "end": {"line": 40, "column": 1}}, "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 40, "column": 1}}, "line": 38}, "7": {"name": "initializeDefaultFlags", "decl": {"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}, "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 58, "column": 1}}, "line": 43}}, "f": {"0": 24, "1": 15, "2": 3, "3": 3, "4": 3, "5": 0, "6": 0, "7": 3}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\ids.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\ids.ts", "all": false, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 32}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 52}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 72}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 1}}}, "s": {"1": 1, "2": 1117, "3": 1117, "4": 1117, "5": 1117, "6": 1117}, "branchMap": {"0": {"type": "branch", "line": 2, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "locations": [{"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}]}, "1": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 72}}, "locations": [{"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 72}}]}}, "b": {"0": [1117], "1": [17872]}, "fnMap": {"0": {"name": "ulid", "decl": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "loc": {"start": {"line": 2, "column": 7}, "end": {"line": 7, "column": 1}}, "line": 2}}, "f": {"0": 1117}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\licensing.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\licensing.ts", "all": false, "statementMap": {"5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 28}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 18}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 111}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 1}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 90}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 1}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 41}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 25}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 23}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 13}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 25}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 145}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 19}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 17}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 3}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 63}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 28}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 25}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 37}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 24}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 79}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 15}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 13}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 1}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 54}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 23}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 46}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 1}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 37}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 23}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 25}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 103}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 10}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 13}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 60}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 4}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 1}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 41}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 23}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 37}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 41}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 10}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 12}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 17}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 66}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 4}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 1}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 71}}}, "s": {"5": 1, "7": 20, "8": 20, "9": 20, "10": 12, "11": 12, "13": 12, "14": 12, "15": 12, "16": 12, "17": 8, "18": 8, "19": 8, "20": 8, "21": 8, "23": 4, "24": 12, "25": 1, "26": 1, "27": 1, "28": 4, "29": 12, "30": 4, "31": 4, "32": 4, "34": 3, "35": 3, "36": 3, "37": 3, "39": 4, "40": 4, "41": 4, "42": 4, "43": 4, "44": 4, "45": 4, "46": 4, "47": 4, "49": 1, "50": 1, "51": 1, "52": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "61": 1}, "branchMap": {"0": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}, "locations": [{"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}]}, "1": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 57}, "end": {"line": 9, "column": 81}}, "locations": [{"start": {"line": 9, "column": 57}, "end": {"line": 9, "column": 81}}]}, "2": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 77}, "end": {"line": 9, "column": 87}}, "locations": [{"start": {"line": 9, "column": 77}, "end": {"line": 9, "column": 87}}]}, "3": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 87}, "end": {"line": 9, "column": 111}}, "locations": [{"start": {"line": 9, "column": 87}, "end": {"line": 9, "column": 111}}]}, "4": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}, "locations": [{"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}]}, "5": {"type": "branch", "line": 11, "loc": {"start": {"line": 11, "column": 80}, "end": {"line": 11, "column": 90}}, "locations": [{"start": {"line": 11, "column": 80}, "end": {"line": 11, "column": 90}}]}, "6": {"type": "branch", "line": 14, "loc": {"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}, "locations": [{"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}]}, "7": {"type": "branch", "line": 17, "loc": {"start": {"line": 17, "column": 12}, "end": {"line": 22, "column": 3}}, "locations": [{"start": {"line": 17, "column": 12}, "end": {"line": 22, "column": 3}}]}, "8": {"type": "branch", "line": 22, "loc": {"start": {"line": 22, "column": 2}, "end": {"line": 25, "column": 27}}, "locations": [{"start": {"line": 22, "column": 2}, "end": {"line": 25, "column": 27}}]}, "9": {"type": "branch", "line": 25, "loc": {"start": {"line": 25, "column": 27}, "end": {"line": 28, "column": 3}}, "locations": [{"start": {"line": 25, "column": 27}, "end": {"line": 28, "column": 3}}]}, "10": {"type": "branch", "line": 28, "loc": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 33}}, "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 33}}]}, "11": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": 58}}, "locations": [{"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": 58}}]}, "12": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 58}, "end": {"line": 30, "column": 79}}, "locations": [{"start": {"line": 30, "column": 58}, "end": {"line": 30, "column": 79}}]}, "13": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 72}, "end": {"line": 33, "column": 1}}, "locations": [{"start": {"line": 30, "column": 72}, "end": {"line": 33, "column": 1}}]}, "14": {"type": "branch", "line": 35, "loc": {"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}, "locations": [{"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}]}, "15": {"type": "branch", "line": 37, "loc": {"start": {"line": 37, "column": 15}, "end": {"line": 37, "column": 46}}, "locations": [{"start": {"line": 37, "column": 15}, "end": {"line": 37, "column": 46}}]}, "16": {"type": "branch", "line": 40, "loc": {"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}, "locations": [{"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}]}, "17": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 97}, "end": {"line": 43, "column": 103}}, "locations": [{"start": {"line": 43, "column": 97}, "end": {"line": 43, "column": 103}}]}, "18": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 30}, "end": {"line": 46, "column": 60}}, "locations": [{"start": {"line": 46, "column": 30}, "end": {"line": 46, "column": 60}}]}, "19": {"type": "branch", "line": 50, "loc": {"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}, "locations": [{"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}]}, "20": {"type": "branch", "line": 58, "loc": {"start": {"line": 58, "column": 58}, "end": {"line": 58, "column": 66}}, "locations": [{"start": {"line": 58, "column": 58}, "end": {"line": 58, "column": 66}}]}}, "b": {"0": [20], "1": [12], "2": [6], "3": [2], "4": [12], "5": [0], "6": [12], "7": [8], "8": [4], "9": [1], "10": [4], "11": [3], "12": [2], "13": [4], "14": [3], "15": [0], "16": [4], "17": [0], "18": [3], "19": [1], "20": [0]}, "fnMap": {"0": {"name": "readLS", "decl": {"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}, "loc": {"start": {"line": 8, "column": 0}, "end": {"line": 10, "column": 1}}, "line": 8}, "1": {"name": "writeLS", "decl": {"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}, "loc": {"start": {"line": 11, "column": 0}, "end": {"line": 12, "column": 1}}, "line": 11}, "2": {"name": "bootstrapLicense", "decl": {"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}, "loc": {"start": {"line": 14, "column": 0}, "end": {"line": 33, "column": 1}}, "line": 14}, "3": {"name": "getEdition", "decl": {"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}, "loc": {"start": {"line": 35, "column": 0}, "end": {"line": 38, "column": 1}}, "line": 35}, "4": {"name": "getTrialInfo", "decl": {"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}, "loc": {"start": {"line": 40, "column": 0}, "end": {"line": 48, "column": 1}}, "line": 40}, "5": {"name": "getLicenseStatus", "decl": {"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}, "loc": {"start": {"line": 50, "column": 0}, "end": {"line": 60, "column": 1}}, "line": 50}}, "f": {"0": 20, "1": 12, "2": 12, "3": 3, "4": 4, "5": 1}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\sw-client.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\sw-client.ts", "all": true, "statementMap": {"2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 86}}}, "s": {"2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 171}, "end": {"line": 3, "column": 86}}, "locations": [{"start": {"line": 1, "column": 171}, "end": {"line": 3, "column": 86}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 171}, "end": {"line": 3, "column": 86}}, "loc": {"start": {"line": 1, "column": 171}, "end": {"line": 3, "column": 86}}, "line": 1}}, "f": {"0": 1}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\units.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\units.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 63}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 75}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 87}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 229}, "end": {"line": 3, "column": 87}}, "locations": [{"start": {"line": 1, "column": 229}, "end": {"line": 3, "column": 87}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 229}, "end": {"line": 3, "column": 87}}, "loc": {"start": {"line": 1, "column": 229}, "end": {"line": 3, "column": 87}}, "line": 1}}, "f": {"0": 0}}, "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\vault.ts": {"path": "C:\\Users\\<USER>\\Downloads\\SizeWise_Offline_App\\lib\\vault.ts", "all": false, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 82}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 39}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 27}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 37}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 50}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 32}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 53}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 19}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 29}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 3}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 1}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 46}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 43}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 68}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 101}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 54}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 42}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 59}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 58}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 81}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 57}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 7}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 63}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 91}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 62}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 62}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 68}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 7}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 6}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 1}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 56}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 7}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 43}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 7}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 24}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 20}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 8}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 44}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 28}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 6}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 19}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 82}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 3}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 1}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 51}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 36}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 80}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 3}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 7}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 35}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 69}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 58}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 45}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 46}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 39}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 29}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 38}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 16}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 15}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 53}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 41}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 28}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 27}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 26}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 11}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 9}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 8}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 109}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 7}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 19}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 79}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 3}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 1}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 62}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 33}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 68}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 56}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 32}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 21}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 8}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 26}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 4}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 43}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 41}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 40}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 102}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 5}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 1}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 96}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 33}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 84}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 73}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 3}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 35}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 36}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 42}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 56}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 7}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 50}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 7}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 24}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 15}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 8}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 10}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 16}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 6}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 40}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 19}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 71}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 3}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 1}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 86}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 33}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 84}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 73}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 3}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 35}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 7}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 50}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 7}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 24}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 31}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 8}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 10}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 15}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 6}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 38}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 37}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 19}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 71}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 3}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 1}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 26}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 30}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 23}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 15}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 59}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 22}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 38}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 47}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 5}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 9}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 44}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 60}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 43}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 49}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 30}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 34}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 11}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 31}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 27}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 8}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 37}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 77}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 65}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 47}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 44}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 55}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 109}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 9}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 21}}, "224": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 62}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 64}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 5}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 3}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 69}}, "231": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 38}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 18}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 5}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 9}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 37}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 76}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 65}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 47}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 46}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 41}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 32}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 26}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 19}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 11}}, "249": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 15}}, "250": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 65}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 90}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 47}}, "253": {"start": {"line": 254, "column": 0}, "end": {"line": 254, "column": 27}}, "254": {"start": {"line": 255, "column": 0}, "end": {"line": 255, "column": 26}}, "255": {"start": {"line": 256, "column": 0}, "end": {"line": 256, "column": 11}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 10}}, "258": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 114}}, "259": {"start": {"line": 260, "column": 0}, "end": {"line": 260, "column": 9}}, "260": {"start": {"line": 261, "column": 0}, "end": {"line": 261, "column": 21}}, "261": {"start": {"line": 262, "column": 0}, "end": {"line": 262, "column": 63}}, "262": {"start": {"line": 263, "column": 0}, "end": {"line": 263, "column": 18}}, "263": {"start": {"line": 264, "column": 0}, "end": {"line": 264, "column": 5}}, "264": {"start": {"line": 265, "column": 0}, "end": {"line": 265, "column": 3}}, "267": {"start": {"line": 268, "column": 0}, "end": {"line": 268, "column": 113}}, "268": {"start": {"line": 269, "column": 0}, "end": {"line": 269, "column": 38}}, "269": {"start": {"line": 270, "column": 0}, "end": {"line": 270, "column": 16}}, "270": {"start": {"line": 271, "column": 0}, "end": {"line": 271, "column": 5}}, "272": {"start": {"line": 273, "column": 0}, "end": {"line": 273, "column": 9}}, "273": {"start": {"line": 274, "column": 0}, "end": {"line": 274, "column": 37}}, "274": {"start": {"line": 275, "column": 0}, "end": {"line": 275, "column": 76}}, "275": {"start": {"line": 276, "column": 0}, "end": {"line": 276, "column": 65}}, "276": {"start": {"line": 277, "column": 0}, "end": {"line": 277, "column": 46}}, "278": {"start": {"line": 279, "column": 0}, "end": {"line": 279, "column": 47}}, "279": {"start": {"line": 280, "column": 0}, "end": {"line": 280, "column": 48}}, "281": {"start": {"line": 282, "column": 0}, "end": {"line": 282, "column": 35}}, "282": {"start": {"line": 283, "column": 0}, "end": {"line": 283, "column": 82}}, "283": {"start": {"line": 284, "column": 0}, "end": {"line": 284, "column": 28}}, "284": {"start": {"line": 285, "column": 0}, "end": {"line": 285, "column": 44}}, "285": {"start": {"line": 286, "column": 0}, "end": {"line": 286, "column": 40}}, "286": {"start": {"line": 287, "column": 0}, "end": {"line": 287, "column": 14}}, "289": {"start": {"line": 290, "column": 0}, "end": {"line": 290, "column": 64}}, "290": {"start": {"line": 291, "column": 0}, "end": {"line": 291, "column": 29}}, "291": {"start": {"line": 292, "column": 0}, "end": {"line": 292, "column": 10}}, "293": {"start": {"line": 294, "column": 0}, "end": {"line": 294, "column": 106}}, "294": {"start": {"line": 295, "column": 0}, "end": {"line": 295, "column": 9}}, "295": {"start": {"line": 296, "column": 0}, "end": {"line": 296, "column": 21}}, "296": {"start": {"line": 297, "column": 0}, "end": {"line": 297, "column": 61}}, "297": {"start": {"line": 298, "column": 0}, "end": {"line": 298, "column": 16}}, "298": {"start": {"line": 299, "column": 0}, "end": {"line": 299, "column": 5}}, "299": {"start": {"line": 300, "column": 0}, "end": {"line": 300, "column": 3}}, "302": {"start": {"line": 303, "column": 0}, "end": {"line": 303, "column": 90}}, "303": {"start": {"line": 304, "column": 0}, "end": {"line": 304, "column": 38}}, "304": {"start": {"line": 305, "column": 0}, "end": {"line": 305, "column": 13}}, "305": {"start": {"line": 306, "column": 0}, "end": {"line": 306, "column": 5}}, "307": {"start": {"line": 308, "column": 0}, "end": {"line": 308, "column": 9}}, "308": {"start": {"line": 309, "column": 0}, "end": {"line": 309, "column": 60}}, "309": {"start": {"line": 310, "column": 0}, "end": {"line": 310, "column": 42}}, "310": {"start": {"line": 311, "column": 0}, "end": {"line": 311, "column": 38}}, "311": {"start": {"line": 312, "column": 0}, "end": {"line": 312, "column": 7}}, "313": {"start": {"line": 314, "column": 0}, "end": {"line": 314, "column": 50}}, "314": {"start": {"line": 315, "column": 0}, "end": {"line": 315, "column": 37}}, "315": {"start": {"line": 316, "column": 0}, "end": {"line": 316, "column": 77}}, "316": {"start": {"line": 317, "column": 0}, "end": {"line": 317, "column": 65}}, "318": {"start": {"line": 319, "column": 0}, "end": {"line": 319, "column": 40}}, "319": {"start": {"line": 320, "column": 0}, "end": {"line": 320, "column": 34}}, "320": {"start": {"line": 321, "column": 0}, "end": {"line": 321, "column": 7}}, "321": {"start": {"line": 322, "column": 0}, "end": {"line": 322, "column": 21}}, "322": {"start": {"line": 323, "column": 0}, "end": {"line": 323, "column": 52}}, "324": {"start": {"line": 325, "column": 0}, "end": {"line": 325, "column": 5}}, "325": {"start": {"line": 326, "column": 0}, "end": {"line": 326, "column": 3}}, "326": {"start": {"line": 327, "column": 0}, "end": {"line": 327, "column": 1}}}, "s": {"1": 1, "7": 1, "8": 1, "9": 1, "10": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "35": 0, "36": 0, "37": 0, "39": 0, "40": 0, "42": 0, "43": 0, "46": 0, "47": 0, "48": 0, "49": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "79": 0, "80": 0, "81": 0, "82": 0, "84": 0, "85": 0, "86": 0, "87": 0, "89": 0, "90": 0, "92": 0, "93": 0, "94": 0, "95": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "115": 0, "116": 0, "117": 0, "118": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "134": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "145": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "164": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "190": 1, "192": 1, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "201": 0, "202": 0, "203": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "214": 0, "215": 0, "216": 0, "218": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 0, "226": 0, "227": 0, "230": 1, "231": 0, "232": 0, "233": 0, "235": 0, "236": 0, "237": 0, "238": 0, "240": 0, "241": 0, "243": 0, "244": 0, "245": 0, "246": 0, "247": 0, "249": 0, "250": 0, "251": 0, "252": 0, "253": 0, "254": 0, "255": 0, "256": 0, "258": 0, "259": 0, "260": 0, "261": 0, "262": 0, "263": 0, "264": 0, "267": 1, "268": 0, "269": 0, "270": 0, "272": 0, "273": 0, "274": 0, "275": 0, "276": 0, "278": 0, "279": 0, "281": 0, "282": 0, "283": 0, "284": 0, "285": 0, "286": 0, "289": 0, "290": 0, "291": 0, "293": 0, "294": 0, "295": 0, "296": 0, "297": 0, "298": 0, "299": 0, "302": 1, "303": 0, "304": 0, "305": 0, "307": 0, "308": 0, "309": 0, "310": 0, "311": 0, "313": 0, "314": 0, "315": 0, "316": 0, "318": 0, "319": 0, "320": 0, "321": 0, "322": 0, "324": 0, "325": 0, "326": 1}, "branchMap": {"0": {"type": "branch", "line": 29, "loc": {"start": {"line": 29, "column": 2}, "end": {"line": 32, "column": 3}}, "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 32, "column": 3}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "VaultError", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 32, "column": 3}}, "loc": {"start": {"line": 29, "column": 2}, "end": {"line": 32, "column": 3}}, "line": 29}, "1": {"name": "openVaultDB", "decl": {"start": {"line": 36, "column": 0}, "end": {"line": 61, "column": 1}}, "loc": {"start": {"line": 36, "column": 0}, "end": {"line": 61, "column": 1}}, "line": 36}, "2": {"name": "generateDeviceKey", "decl": {"start": {"line": 64, "column": 0}, "end": {"line": 77, "column": 1}}, "loc": {"start": {"line": 64, "column": 0}, "end": {"line": 77, "column": 1}}, "line": 64}, "3": {"name": "getDeviceKey", "decl": {"start": {"line": 80, "column": 0}, "end": {"line": 113, "column": 1}}, "loc": {"start": {"line": 80, "column": 0}, "end": {"line": 113, "column": 1}}, "line": 80}, "4": {"name": "storeDeviceKey", "decl": {"start": {"line": 116, "column": 0}, "end": {"line": 132, "column": 1}}, "loc": {"start": {"line": 116, "column": 0}, "end": {"line": 132, "column": 1}}, "line": 116}, "5": {"name": "encryptData", "decl": {"start": {"line": 135, "column": 0}, "end": {"line": 162, "column": 1}}, "loc": {"start": {"line": 135, "column": 0}, "end": {"line": 162, "column": 1}}, "line": 135}, "6": {"name": "decryptData", "decl": {"start": {"line": 165, "column": 0}, "end": {"line": 188, "column": 1}}, "loc": {"start": {"line": 165, "column": 0}, "end": {"line": 188, "column": 1}}, "line": 165}, "7": {"name": "createSnapshot", "decl": {"start": {"line": 193, "column": 2}, "end": {"line": 228, "column": 3}}, "loc": {"start": {"line": 193, "column": 2}, "end": {"line": 228, "column": 3}}, "line": 193}, "8": {"name": "getSnapshot", "decl": {"start": {"line": 231, "column": 2}, "end": {"line": 265, "column": 3}}, "loc": {"start": {"line": 231, "column": 2}, "end": {"line": 265, "column": 3}}, "line": 231}, "9": {"name": "listSnapshots", "decl": {"start": {"line": 268, "column": 2}, "end": {"line": 300, "column": 3}}, "loc": {"start": {"line": 268, "column": 2}, "end": {"line": 300, "column": 3}}, "line": 268}, "10": {"name": "cleanupSnapshots", "decl": {"start": {"line": 303, "column": 2}, "end": {"line": 326, "column": 3}}, "loc": {"start": {"line": 303, "column": 2}, "end": {"line": 326, "column": 3}}, "line": 303}}, "f": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}}}