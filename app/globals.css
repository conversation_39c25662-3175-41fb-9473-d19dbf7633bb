:root {
  --bg: #0f1216;
  --glass: rgba(255, 255, 255, 0.06);
  --stroke: rgba(255, 255, 255, 0.12);
  --text: #e7ecf3;
  --muted: #9aa4b2;
}
* {
  box-sizing: border-box;
}
html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: ui-sans-serif, system-ui, Segoe UI, Roboto, Helvetica, Arial;
  color: var(--text);
  background: radial-gradient(
      1200px 800px at 10% -10%,
      #1a2030 0%,
      transparent 60%
    ),
    radial-gradient(1200px 800px at 110% 10%, #1b2a3a 0%, transparent 60%),
    var(--bg);
}
.container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 32px;
}
.topbar {
  position: sticky;
  top: 0;
  backdrop-filter: blur(10px);
  background: var(--glass);
  border-bottom: 1px solid var(--stroke);
  z-index: 10;
}
.row {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
}
.btn {
  appearance: none;
  border: 1px solid var(--stroke);
  background: var(--glass);
  color: var(--text);
  padding: 10px 14px;
  border-radius: 10px;
  cursor: pointer;
}
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
}
.card {
  border: 1px solid var(--stroke);
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.06),
    rgba(255, 255, 255, 0.03)
  );
  border-radius: 16px;
  padding: 18px;
  transition: transform 0.16s ease, border-color 0.16s ease;
  cursor: pointer;
}
.card:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.22);
}
.card h3 {
  margin: 0 0 8px 0;
}
.badge {
  display: inline-block;
  font-size: 12px;
  color: var(--muted);
  border: 1px dashed var(--stroke);
  padding: 4px 8px;
  border-radius: 999px;
}
.badge-trial {
  background: rgba(74, 158, 255, 0.1);
  border: 1px solid rgba(74, 158, 255, 0.3);
  color: #4a9eff;
}
.badge-warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #ffc107;
}
.badge-expired {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
}
.badge-free {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
  color: #28a745;
}
.badge-licensed {
  background: rgba(111, 66, 193, 0.1);
  border: 1px solid rgba(111, 66, 193, 0.3);
  color: #6f42c1;
}
.hero {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  margin: 24px 0 28px 0;
}
.hero h1 {
  margin: 0;
  font-size: 28px;
}
.panel {
  border: 1px solid var(--stroke);
  background: var(--glass);
  border-radius: 14px;
  padding: 16px;
}
.muted {
  color: var(--muted);
}
.center {
  display: flex;
  min-height: 100dvh;
  align-items: center;
  justify-content: center;
  padding: 24px;
}
.link {
  color: #9cc6ff;
  text-decoration: none;
}
.toast {
  position: fixed;
  right: 16px;
  bottom: 16px;
  border: 1px solid var(--stroke);
  background: var(--glass);
  backdrop-filter: blur(8px);
  padding: 10px 14px;
  border-radius: 12px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}
.modal-content {
  background: var(--bg);
  border: 1px solid var(--stroke);
  border-radius: 16px;
  padding: 0;
  max-width: 500px;
  width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--stroke);
}
.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
}
.modal-close {
  background: none;
  border: none;
  color: var(--muted);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-close:hover {
  color: var(--text);
}
.modal-content form {
  padding: 24px;
}
.form-group {
  margin-bottom: 20px;
}
.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text);
}
.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--stroke);
  background: var(--glass);
  color: var(--text);
  border-radius: 8px;
  font-family: inherit;
}
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4a9eff;
}
.form-group textarea {
  resize: vertical;
  min-height: 60px;
}
.limit-warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}
.limit-warning strong {
  color: #ffc107;
}
.error-message {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #dc3545;
}
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--stroke);
}
.btn-primary {
  background: #4a9eff;
  border: 1px solid #4a9eff;
  color: white;
}
.btn-primary:hover:not(:disabled) {
  background: #3a8eef;
}
.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
