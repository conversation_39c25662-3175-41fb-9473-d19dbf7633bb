name: CI

on:
  pull_request:
    branches: ["main"]
  push:
    branches: ["main"]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    outputs:
      run-e2e: ${{ steps.diff.outputs.app_changed }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npm run typecheck
        continue-on-error: true

      - name: Lint
        run: npm run lint
        env:
          CI: true
        continue-on-error: true

      - name: Run unit and integration tests
        run: npm run test:coverage
      - name: Determine if app-impacting files changed
        id: diff
        run: |
          echo "app_changed=false" >> $GITHUB_OUTPUT
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            git fetch --no-tags --prune --depth=1 origin "${{ github.base_ref }}"
            BASE_SHA=$(git rev-parse origin/${{ github.base_ref }})
            HEAD_SHA=${{ github.sha }}
            echo "Comparing $BASE_SHA...$HEAD_SHA"
            CHANGED=$(git diff --name-only "$BASE_SHA" "$HEAD_SHA" | tr '\n' ' ')
          else
            # push to main: always consider as impacting
            CHANGED="force-run-on-push"
          fi
          echo "Changed files: $CHANGED"
          case "$CHANGED" in
            *"app/"*|*"components/"*|*"lib/"*|*"core/"*|*"db/"*|*"public/"*|*"e2e/"*|*"next.config"*|*"package.json"*|*"package-lock.json"*|*"pnpm-lock.yaml"*|*"yarn.lock"*|*"playwright.config"*)
              echo "app_changed=true" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "app_changed=false" >> $GITHUB_OUTPUT
              ;;
          esac

      - name: Enforce coverage thresholds
        run: |
          node - <<'NODE'
          const fs = require('fs');

          const summaryPath = './coverage/coverage-summary.json';
          const finalPath = './coverage/coverage-final.json';

          if (!fs.existsSync(summaryPath)) {
            console.error('Coverage file not found');
            process.exit(1);
          }

          const coverage = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
          const { lines, functions, branches, statements } = coverage.total;

          console.log('Coverage Summary:');
          console.log('Lines:', lines.pct + '%');
          console.log('Functions:', functions.pct + '%');
          console.log('Branches:', branches.pct + '%');
          console.log('Statements:', statements.pct + '%');

          const coreThreshold = 85;

          try {
            if (!fs.existsSync(finalPath)) throw new Error('coverage-final.json missing');
            const details = JSON.parse(fs.readFileSync(finalPath, 'utf8'));
            const files = Object.keys(details);
            const isCore = (p) => p.startsWith('lib/') || p.startsWith('db/') || p.startsWith('core/');
            let coveredLines = 0, totalLines = 0, coveredFuncs = 0, totalFuncs = 0;
            for (const f of files) {
              if (!isCore(f)) continue;
              const m = details[f];
              coveredLines += m.l;
              totalLines += m.l + m.u;
              coveredFuncs += m.f;
              totalFuncs += m.f + m.uf;
            }
            const linePct = totalLines ? Math.round((coveredLines / totalLines) * 100) : 100;
            const funcPct = totalFuncs ? Math.round((coveredFuncs / totalFuncs) * 100) : 100;
            console.log(`Core coverage -> Lines: ${linePct}% Functions: ${funcPct}%`);
            if (linePct < coreThreshold) {
              console.error(`Core line coverage (${linePct}%) is below threshold (${coreThreshold}%)`);
              process.exit(1);
            }
            if (funcPct < coreThreshold) {
              console.error(`Core function coverage (${funcPct}%) is below threshold (${coreThreshold}%)`);
              process.exit(1);
            }
          } catch (e) {
            console.warn('Could not compute per-directory core coverage, falling back to total. Reason:', e.message);
            if (lines.pct < coreThreshold || functions.pct < coreThreshold) {
              console.error('Coverage below threshold');
              process.exit(1);
            }
          }

          console.log('Coverage thresholds met');
          NODE

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/

  e2e:
    runs-on: ubuntu-latest
    needs: build-test
    if: github.event_name == 'push' || needs.build-test.outputs.run-e2e == 'true'
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps chromium

      - name: Run E2E tests (Playwright manages server)
        run: npm run e2e

      - name: Upload E2E test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
