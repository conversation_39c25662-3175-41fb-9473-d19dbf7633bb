---
name: Bug Report
about: Report a bug in SizeWise
title: '[BUG] '
labels: ['bug']
assignees: ''
---

## Bug Description
<!-- A clear and concise description of what the bug is -->

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

## Expected Behavior
<!-- What you expected to happen -->

## Actual Behavior
<!-- What actually happened -->

## Screenshots
<!-- If applicable, add screenshots to help explain your problem -->

## Environment
- **Browser**: [e.g. Chrome 120, Firefox 121]
- **OS**: [e.g. Windows 11, macOS 14]
- **Device**: [e.g. Desktop, Tablet]
- **SizeWise Version**: [e.g. 1.0.0]

## Console Errors
<!-- Any errors in the browser console -->
```
Paste console errors here
```

## Additional Context
<!-- Any other context about the problem -->

## Suggested Tests
<!-- Tests that should be added to prevent regression -->

### Unit Tests
- [ ] Test for the specific failing condition
- [ ] Test edge cases around the bug

### Integration Tests
- [ ] Test the full workflow that's failing
- [ ] Test error handling for this scenario

### E2E Tests
- [ ] Test the user journey that revealed the bug
- [ ] Test offline behavior if relevant

## Priority
- [ ] Critical (blocks core functionality)
- [ ] High (impacts user experience significantly)  
- [ ] Medium (minor impact on user experience)
- [ ] Low (cosmetic or edge case)
